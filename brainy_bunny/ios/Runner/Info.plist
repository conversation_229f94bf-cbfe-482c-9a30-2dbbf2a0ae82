<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Brainy Bunny</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>brainy_bunny</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<true/>
		<key>UIStatusBarStyle</key>
		<string></string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>

		<!-- Adjust SDK: SKAN Direct Install Postbacks to Rocapine endpoint -->
		<key>NSAdvertisingAttributionReportEndpoint</key>
		<string>https://skan-rocapine.com</string>

		<!-- Meta SDK (Facebook SDK) Configuration -->
		<key>FacebookAppID</key>
		<string>2012260112880464</string>

		<key>FacebookClientToken</key>
		<string>********************************</string>

		<key>FacebookDisplayName</key>
		<string>Brainy Bunny</string>

		<!-- Facebook Auto Log App Events -->
		<key>FacebookAutoLogAppEventsEnabled</key>
		<true/>

		<!-- Facebook Advertiser ID Collection -->
		<key>FacebookAdvertiserIDCollectionEnabled</key>
		<true/>

		<!-- URL Schemes for Facebook -->
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string>com.goodkarmalab.brainyBunny</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb2012260112880464</string>
				</array>
			</dict>
		</array>

		<!-- LSApplicationQueriesSchemes for Facebook -->
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
		</array>
	</dict>
</plist>
