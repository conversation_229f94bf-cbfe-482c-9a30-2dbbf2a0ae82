// lib/constants/adjust_constants.dart
import 'dart:io';

/// Constants for Adjust SDK configuration and event tracking
class AdjustConstants {
  // App Configuration
  static const String APP_TOKEN = '2k0f6660xxxc';
  
  // Environment Configuration
  static const bool IS_PRODUCTION = false; // Set to true for production builds
  
  // SKAN Configuration
  static const String SKAN_POSTBACK_URL = 'https://skan-rocapine.com';
  
  // Event Tokens - Replace with actual tokens from Adjust Dashboard
  // These are placeholder tokens - you need to create events in Adjust Dashboard
  static const String EVENT_APP_LAUNCH = 'app_launch_token';
  static const String EVENT_GAME_START = 'game_start_token';
  static const String EVENT_GAME_COMPLETE = 'game_complete_token';
  static const String EVENT_LEVEL_COMPLETE = 'level_complete_token';
  static const String EVENT_TUTORIAL_COMPLETE = 'tutorial_complete_token';
  static const String EVENT_TRIAL_START = 'trial_start_token';
  static const String EVENT_TRIAL_COMPLETE = 'trial_complete_token';
  static const String EVENT_PAYWALL_VIEW = 'paywall_view_token';
  static const String EVENT_PARENTAL_GATE_VIEW = 'parental_gate_view_token';
  static const String EVENT_PARENTAL_GATE_SUCCESS = 'parental_gate_success_token';
  static const String EVENT_PURCHASE_ATTEMPT = 'purchase_attempt_token';
  static const String EVENT_PURCHASE_SUCCESS = 'purchase_success_token';
  static const String EVENT_SUBSCRIPTION_START = 'subscription_start_token';
  static const String EVENT_SUBSCRIPTION_RENEWAL = 'subscription_renewal_token';
  
  // Revenue Events
  static const String REVENUE_EVENT_SUBSCRIPTION = 'subscription_revenue_token';
  static const String REVENUE_EVENT_TRIAL_CONVERSION = 'trial_conversion_token';
  
  // Callback Parameters
  static const String PARAM_GAME_TYPE = 'game_type';
  static const String PARAM_LEVEL = 'level';
  static const String PARAM_SCORE = 'score';
  static const String PARAM_DURATION = 'duration';
  static const String PARAM_SUCCESS = 'success';
  static const String PARAM_ATTEMPTS = 'attempts';
  static const String PARAM_PRODUCT_ID = 'product_id';
  static const String PARAM_PRICE = 'price';
  static const String PARAM_CURRENCY = 'currency';
  static const String PARAM_SUBSCRIPTION_TYPE = 'subscription_type';
  static const String PARAM_TRIAL_PERIOD = 'trial_period';
  static const String PARAM_TRIGGERED_FROM = 'triggered_from';
  static const String PARAM_USER_TYPE = 'user_type';
  static const String PARAM_SESSION_COUNT = 'session_count';
  
  // Partner Parameters (for external integrations)
  static const String PARTNER_FACEBOOK = 'facebook';
  static const String PARTNER_GOOGLE = 'google';
  static const String PARTNER_APPLE = 'apple';
  
  // Configuration Check
  static bool get isConfigured {
    return APP_TOKEN.isNotEmpty && APP_TOKEN != 'your_app_token_here';
  }
  
  // Platform-specific configuration
  static bool get isConfiguredForCurrentPlatform {
    if (Platform.isIOS || Platform.isAndroid) {
      return isConfigured;
    }
    return false; // Adjust SDK only supports iOS and Android
  }
  
  // Debug information
  static Map<String, dynamic> get debugInfo {
    return {
      'app_token': APP_TOKEN,
      'is_production': IS_PRODUCTION,
      'is_configured': isConfigured,
      'platform_supported': Platform.isIOS || Platform.isAndroid,
      'skan_url': SKAN_POSTBACK_URL,
    };
  }
  
  // Game Types for Analytics
  static const Map<int, String> GAME_TYPES = {
    1: 'shape_matching',
    2: 'color_sorting',
    3: 'number_counting',
    4: 'pattern_recognition',
    5: 'memory_game',
    6: 'puzzle_solving',
    7: 'logic_game',
    8: 'spatial_reasoning',
    9: 'sequence_learning',
    10: 'problem_solving',
    11: 'creative_thinking',
    12: 'motor_skills',
    13: 'language_learning',
    14: 'math_basics',
    15: 'science_exploration',
  };
  
  // Subscription Types
  static const Map<String, String> SUBSCRIPTION_TYPES = {
    'trial': 'free_trial',
    'monthly': 'monthly_subscription',
    'yearly': 'yearly_subscription',
    'lifetime': 'lifetime_purchase',
  };
  
  // User Types
  static const Map<String, String> USER_TYPES = {
    'new': 'new_user',
    'returning': 'returning_user',
    'trial': 'trial_user',
    'premium': 'premium_user',
  };
  
  // Standard Currencies
  static const String CURRENCY_USD = 'USD';
  static const String CURRENCY_EUR = 'EUR';
  static const String CURRENCY_GBP = 'GBP';
  
  // Default Values
  static const String DEFAULT_CURRENCY = CURRENCY_USD;
  static const double DEFAULT_TRIAL_VALUE = 0.0;
}
