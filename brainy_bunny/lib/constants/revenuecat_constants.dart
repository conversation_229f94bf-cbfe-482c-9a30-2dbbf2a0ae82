// lib/constants/revenuecat_constants.dart
import 'dart:io';

/// Constants for RevenueCat SDK configuration
class RevenueCatConstants {
  // API Keys - TODO: Replace with your actual RevenueCat API keys from RevenueCat Dashboard
  static const String IOS_API_KEY = 'appl_your_ios_api_key_here';
  static const String ANDROID_API_KEY = 'goog_your_android_api_key_here';
  
  // Get platform-specific API key
  static String get API_KEY {
    if (Platform.isIOS) {
      return IOS_API_KEY;
    } else if (Platform.isAndroid) {
      return ANDROID_API_KEY;
    } else {
      return '';
    }
  }
  
  // Offering Configuration
  static const String DEFAULT_OFFERING_ID = 'default';
  static const String PREMIUM_OFFERING_ID = 'premium';
  
  // Product IDs for Subscriptions
  // iOS Product IDs (from App Store Connect)
  static const String IOS_WEEKLY_TRIAL_ID = 'brainy_bunny_weekly_trial';
  static const String IOS_MONTHLY_SUBSCRIPTION_ID = 'brainy_bunny_monthly';
  static const String IOS_YEARLY_SUBSCRIPTION_ID = 'brainy_bunny_yearly';
  
  // Android Product IDs (from Google Play Console)
  static const String ANDROID_WEEKLY_TRIAL_ID = 'brainy_bunny_weekly_trial';
  static const String ANDROID_MONTHLY_SUBSCRIPTION_ID = 'brainy_bunny_monthly';
  static const String ANDROID_YEARLY_SUBSCRIPTION_ID = 'brainy_bunny_yearly';
  
  // Platform-specific product IDs
  static String get WEEKLY_TRIAL_ID {
    return Platform.isIOS ? IOS_WEEKLY_TRIAL_ID : ANDROID_WEEKLY_TRIAL_ID;
  }
  
  static String get MONTHLY_SUBSCRIPTION_ID {
    return Platform.isIOS ? IOS_MONTHLY_SUBSCRIPTION_ID : ANDROID_MONTHLY_SUBSCRIPTION_ID;
  }
  
  static String get YEARLY_SUBSCRIPTION_ID {
    return Platform.isIOS ? IOS_YEARLY_SUBSCRIPTION_ID : ANDROID_YEARLY_SUBSCRIPTION_ID;
  }
  
  // All product IDs for easy reference
  static Set<String> get ALL_PRODUCT_IDS {
    return {
      WEEKLY_TRIAL_ID,
      MONTHLY_SUBSCRIPTION_ID,
      YEARLY_SUBSCRIPTION_ID,
    };
  }
  
  // Entitlement IDs
  static const String PREMIUM_ENTITLEMENT_ID = 'premium';
  static const String FULL_ACCESS_ENTITLEMENT_ID = 'full_access';
  
  // Package Types
  static const String PACKAGE_TYPE_WEEKLY = 'weekly';
  static const String PACKAGE_TYPE_MONTHLY = 'monthly';
  static const String PACKAGE_TYPE_ANNUAL = 'annual';
  static const String PACKAGE_TYPE_LIFETIME = 'lifetime';
  
  // Subscription Periods
  static const String PERIOD_WEEKLY = 'P1W';
  static const String PERIOD_MONTHLY = 'P1M';
  static const String PERIOD_YEARLY = 'P1Y';
  
  // Trial Periods
  static const String TRIAL_PERIOD_3_DAYS = 'P3D';
  static const String TRIAL_PERIOD_7_DAYS = 'P7D';
  static const String TRIAL_PERIOD_14_DAYS = 'P14D';
  
  // Default trial period
  static const String DEFAULT_TRIAL_PERIOD = TRIAL_PERIOD_7_DAYS;
  
  // Pricing Configuration
  static const Map<String, double> DEFAULT_PRICES = {
    PACKAGE_TYPE_WEEKLY: 2.99,
    PACKAGE_TYPE_MONTHLY: 9.99,
    PACKAGE_TYPE_ANNUAL: 59.99,
  };
  
  // Currency
  static const String DEFAULT_CURRENCY = 'USD';
  
  // Configuration Check
  static bool get isConfigured {
    return API_KEY.isNotEmpty &&
           API_KEY != 'appl_your_ios_api_key_here' &&
           API_KEY != 'goog_your_android_api_key_here';
  }
  
  // Platform-specific configuration
  static bool get isConfiguredForCurrentPlatform {
    if (Platform.isIOS || Platform.isAndroid) {
      return isConfigured;
    }
    return false; // RevenueCat only supports iOS and Android
  }
  
  // Debug information
  static Map<String, dynamic> get debugInfo {
    return {
      'api_key_configured': isConfigured,
      'platform': Platform.operatingSystem,
      'platform_supported': Platform.isIOS || Platform.isAndroid,
      'weekly_trial_id': WEEKLY_TRIAL_ID,
      'monthly_subscription_id': MONTHLY_SUBSCRIPTION_ID,
      'yearly_subscription_id': YEARLY_SUBSCRIPTION_ID,
      'default_offering': DEFAULT_OFFERING_ID,
      'premium_entitlement': PREMIUM_ENTITLEMENT_ID,
    };
  }
  
  // Subscription Benefits
  static const List<String> PREMIUM_BENEFITS = [
    'Unlock all 15 educational games',
    'No ads or interruptions',
    'Progress tracking and analytics',
    'Offline play capability',
    'Regular content updates',
    'Priority customer support',
  ];
  
  // Paywall Configuration
  static const String PAYWALL_TITLE = 'Unlock Full Learning Experience';
  static const String PAYWALL_SUBTITLE = 'Give your child access to all educational games and features';
  
  // Call-to-Action Buttons
  static const String CTA_START_TRIAL = 'Start Free Trial';
  static const String CTA_SUBSCRIBE = 'Subscribe Now';
  static const String CTA_CONTINUE = 'Continue';
  static const String CTA_RESTORE = 'Restore Purchases';
  
  // Legal Text
  static const String TERMS_URL = 'https://your-app.com/terms';
  static const String PRIVACY_URL = 'https://your-app.com/privacy';
  
  // Error Messages
  static const String ERROR_NETWORK = 'Network error. Please check your connection and try again.';
  static const String ERROR_PURCHASE_CANCELLED = 'Purchase was cancelled.';
  static const String ERROR_PURCHASE_FAILED = 'Purchase failed. Please try again.';
  static const String ERROR_RESTORE_FAILED = 'Failed to restore purchases. Please try again.';
  static const String ERROR_NO_PRODUCTS = 'No subscription products available.';
  static const String ERROR_ALREADY_SUBSCRIBED = 'You already have an active subscription.';
  
  // Success Messages
  static const String SUCCESS_PURCHASE = 'Welcome to Premium! All games are now unlocked.';
  static const String SUCCESS_RESTORE = 'Purchases restored successfully!';
  static const String SUCCESS_TRIAL_START = 'Free trial started! Enjoy full access.';
  
  // Analytics Events
  static const String EVENT_PAYWALL_SHOWN = 'paywall_shown';
  static const String EVENT_PURCHASE_STARTED = 'purchase_started';
  static const String EVENT_PURCHASE_COMPLETED = 'purchase_completed';
  static const String EVENT_PURCHASE_CANCELLED = 'purchase_cancelled';
  static const String EVENT_PURCHASE_FAILED = 'purchase_failed';
  static const String EVENT_RESTORE_STARTED = 'restore_started';
  static const String EVENT_RESTORE_COMPLETED = 'restore_completed';
  static const String EVENT_TRIAL_STARTED = 'trial_started';
  static const String EVENT_SUBSCRIPTION_RENEWED = 'subscription_renewed';
  static const String EVENT_SUBSCRIPTION_CANCELLED = 'subscription_cancelled';
  
  // User Properties
  static const String USER_PROPERTY_SUBSCRIPTION_STATUS = 'subscription_status';
  static const String USER_PROPERTY_TRIAL_STATUS = 'trial_status';
  static const String USER_PROPERTY_SUBSCRIPTION_TYPE = 'subscription_type';
  static const String USER_PROPERTY_PURCHASE_DATE = 'purchase_date';
  static const String USER_PROPERTY_TRIAL_START_DATE = 'trial_start_date';
  
  // Subscription Types
  static const Map<String, String> SUBSCRIPTION_TYPES = {
    'trial': 'free_trial',
    'weekly': 'weekly_subscription',
    'monthly': 'monthly_subscription',
    'yearly': 'yearly_subscription',
    'lifetime': 'lifetime_subscription',
  };

  // Subscription Status Values
  static const String STATUS_ACTIVE = 'active';
  static const String STATUS_TRIAL = 'trial';
  static const String STATUS_EXPIRED = 'expired';
  static const String STATUS_CANCELLED = 'cancelled';
  static const String STATUS_FREE = 'free';
  
  // Grace Period (for failed payments)
  static const int GRACE_PERIOD_DAYS = 3;
  
  // Retry Configuration
  static const int MAX_RETRY_ATTEMPTS = 3;
  static const Duration RETRY_DELAY = Duration(seconds: 2);
  
  // Cache Configuration
  static const Duration CACHE_DURATION = Duration(hours: 1);
  static const Duration OFFERINGS_CACHE_DURATION = Duration(minutes: 30);
}
