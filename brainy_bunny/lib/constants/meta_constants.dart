// lib/constants/meta_constants.dart
import 'dart:io';

/// Constants for Meta SDK (Facebook SDK) configuration
class MetaConstants {
  // App Configuration
  static const String FACEBOOK_APP_ID = '2012260112880464';
  static const String FACEBOOK_CLIENT_TOKEN = '********************************';
  
  // App Events Configuration
  static const bool AUTO_LOG_APP_EVENTS = true;
  static const bool ADVERTISER_ID_COLLECTION = true;
  
  // Facebook Standard Events - User Journey
  static const String EVENT_COMPLETED_TUTORIAL = 'CompletedTutorial';
  static const String EVENT_START_TRIAL = 'StartTrial';
  static const String EVENT_SUBSCRIBE = 'Subscribe';
  
  // Standard Events - App Lifecycle
  static const String EVENT_APP_LAUNCHED = 'fb_mobile_app_launched';
  static const String EVENT_APP_INSTALLED = 'fb_mobile_app_install';
  static const String EVENT_SESSION_START = 'fb_mobile_session_start';
  
  // Standard Events - Game Progress
  static const String EVENT_LEVEL_ACHIEVED = 'fb_mobile_level_achieved';
  static const String EVENT_TUTORIAL_COMPLETED = 'fb_mobile_tutorial_completed';
  static const String EVENT_CONTENT_VIEW = 'fb_mobile_content_view';
  
  // Standard Events - Purchase Flow
  static const String EVENT_PURCHASE = 'fb_mobile_purchase';
  static const String EVENT_ADD_TO_CART = 'fb_mobile_add_to_cart';
  static const String EVENT_INITIATED_CHECKOUT = 'fb_mobile_initiated_checkout';
  static const String EVENT_ADD_PAYMENT_INFO = 'fb_mobile_add_payment_info';
  
  // Custom Events - Brainy Bunny Specific
  static const String EVENT_GAME_STARTED = 'brainy_bunny_game_started';
  static const String EVENT_GAME_COMPLETED = 'brainy_bunny_game_completed';
  static const String EVENT_LEVEL_UNLOCKED = 'brainy_bunny_level_unlocked';
  static const String EVENT_PAYWALL_VIEWED = 'brainy_bunny_paywall_viewed';
  static const String EVENT_PARENTAL_GATE_VIEWED = 'brainy_bunny_parental_gate_viewed';
  static const String EVENT_PARENTAL_GATE_PASSED = 'brainy_bunny_parental_gate_passed';
  static const String EVENT_TRIAL_COMPLETED = 'brainy_bunny_trial_completed';
  static const String EVENT_PREMIUM_UNLOCKED = 'brainy_bunny_premium_unlocked';
  
  // Parameter Names
  static const String PARAM_LEVEL = 'level';
  static const String PARAM_GAME_TYPE = 'game_type';
  static const String PARAM_SCORE = 'score';
  static const String PARAM_DURATION = 'duration';
  static const String PARAM_SUCCESS = 'success';
  static const String PARAM_CURRENCY = 'fb_currency';
  static const String PARAM_VALUE = 'fb_value';
  static const String PARAM_CONTENT_TYPE = 'fb_content_type';
  static const String PARAM_CONTENT_ID = 'fb_content_id';
  static const String PARAM_DESCRIPTION = 'fb_description';
  
  // Purchase Parameters
  static const String PARAM_PRODUCT_ID = 'product_id';
  static const String PARAM_PURCHASE_TYPE = 'purchase_type';
  static const String PARAM_PAYMENT_METHOD = 'payment_method';
  static const String PARAM_TRANSACTION_ID = 'transaction_id';
  static const String PARAM_SUBSCRIPTION_TYPE = 'subscription_type';
  static const String PARAM_TRIAL_PERIOD = 'trial_period';
  
  // Game-specific Parameters
  static const String PARAM_ATTEMPTS = 'attempts';
  static const String PARAM_HINTS_USED = 'hints_used';
  static const String PARAM_TIME_SPENT = 'time_spent';
  static const String PARAM_DIFFICULTY = 'difficulty';
  static const String PARAM_AGE_GROUP = 'age_group';
  static const String PARAM_TRIGGERED_FROM = 'triggered_from';
  
  // Configuration Check
  static bool get isConfigured {
    return FACEBOOK_APP_ID.isNotEmpty && 
           FACEBOOK_CLIENT_TOKEN.isNotEmpty &&
           FACEBOOK_APP_ID != 'YOUR_FACEBOOK_APP_ID_HERE' &&
           FACEBOOK_CLIENT_TOKEN != 'YOUR_FACEBOOK_CLIENT_TOKEN_HERE';
  }
  
  // Platform-specific configuration
  static bool get isConfiguredForCurrentPlatform {
    if (Platform.isIOS || Platform.isAndroid) {
      return isConfigured;
    }
    return false; // Meta SDK only supports iOS and Android
  }
  
  // Debug information
  static Map<String, dynamic> get debugInfo {
    return {
      'facebook_app_id': FACEBOOK_APP_ID,
      'client_token_configured': FACEBOOK_CLIENT_TOKEN.isNotEmpty,
      'is_configured': isConfigured,
      'platform_supported': Platform.isIOS || Platform.isAndroid,
      'auto_log_enabled': AUTO_LOG_APP_EVENTS,
      'advertiser_id_collection': ADVERTISER_ID_COLLECTION,
    };
  }
  
  // Game Types for Analytics
  static const Map<int, String> GAME_TYPES = {
    1: 'shape_matching',
    2: 'color_sorting',
    3: 'number_counting',
    4: 'pattern_recognition',
    5: 'memory_game',
    6: 'puzzle_solving',
    7: 'logic_game',
    8: 'spatial_reasoning',
    9: 'sequence_learning',
    10: 'problem_solving',
    11: 'creative_thinking',
    12: 'motor_skills',
    13: 'language_learning',
    14: 'math_basics',
    15: 'science_exploration',
  };
  
  // Age Groups for Analytics
  static const Map<String, String> AGE_GROUPS = {
    'toddler': '2-3 years',
    'preschool': '3-5 years',
    'kindergarten': '5-6 years',
    'early_elementary': '6-8 years',
  };
  
  // Difficulty Levels
  static const Map<String, int> DIFFICULTY_LEVELS = {
    'easy': 1,
    'medium': 2,
    'hard': 3,
    'expert': 4,
  };
  
  // Standard Values for Purchase Events
  static const String CURRENCY_USD = 'USD';
  static const String CONTENT_TYPE_PRODUCT = 'product';
  static const String PURCHASE_TYPE_SUBSCRIPTION = 'subscription';
  static const String PAYMENT_METHOD_APP_STORE = 'app_store';
  static const String PAYMENT_METHOD_GOOGLE_PLAY = 'google_play';
  
  // Subscription Types
  static const Map<String, String> SUBSCRIPTION_TYPES = {
    'trial': 'free_trial',
    'weekly': 'weekly_subscription',
    'monthly': 'monthly_subscription',
    'yearly': 'yearly_subscription',
  };
  
  // Trial Types
  static const Map<String, String> TRIAL_TYPES = {
    'free_levels': 'free_levels_trial',
    'time_based': 'time_based_trial',
    'feature_trial': 'feature_trial',
  };
}
