// lib/services/revenuecat_service.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:brainy_bunny/constants/revenuecat_constants.dart';

/// RevenueCat service for subscription management
/// Handles subscription purchases, trials, and entitlement checking
class RevenueCatService {
  // Singleton pattern
  static final RevenueCatService _instance = RevenueCatService._internal();
  static RevenueCatService get instance => _instance;
  RevenueCatService._internal();

  // State
  bool _isInitialized = false;
  bool _isEnabled = true;
  CustomerInfo? _customerInfo;
  Offerings? _offerings;
  
  // Stream controllers for reactive updates
  final StreamController<bool> _subscriptionStatusController = StreamController<bool>.broadcast();
  final StreamController<CustomerInfo> _customerInfoController = StreamController<CustomerInfo>.broadcast();

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled && RevenueCatConstants.isConfiguredForCurrentPlatform;
  CustomerInfo? get customerInfo => _customerInfo;
  Offerings? get offerings => _offerings;
  
  // Streams
  Stream<bool> get subscriptionStatusStream => _subscriptionStatusController.stream;
  Stream<CustomerInfo> get customerInfoStream => _customerInfoController.stream;

  /// Check if user has premium access
  bool get hasPremiumAccess {
    if (_customerInfo == null) return false;
    
    // Check for active entitlements
    final entitlements = _customerInfo!.entitlements.active;
    return entitlements.containsKey(RevenueCatConstants.PREMIUM_ENTITLEMENT_ID) ||
           entitlements.containsKey(RevenueCatConstants.FULL_ACCESS_ENTITLEMENT_ID);
  }

  /// Check if user is in trial period
  bool get isInTrial {
    if (_customerInfo == null) return false;
    
    final entitlements = _customerInfo!.entitlements.active;
    for (final entitlement in entitlements.values) {
      if (entitlement.periodType == PeriodType.trial) {
        return true;
      }
    }
    return false;
  }

  /// Get subscription status
  String get subscriptionStatus {
    if (!hasPremiumAccess) return RevenueCatConstants.STATUS_FREE;
    if (isInTrial) return RevenueCatConstants.STATUS_TRIAL;
    return RevenueCatConstants.STATUS_ACTIVE;
  }

  /// Initialize RevenueCat SDK
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ RevenueCat SDK already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 === REVENUECAT SDK INITIALIZATION ===');
        print('   Platform: ${Platform.operatingSystem}');
        print('   API Key: ${RevenueCatConstants.API_KEY}');
      }

      // Check if RevenueCat SDK is configured
      if (!RevenueCatConstants.isConfiguredForCurrentPlatform) {
        if (kDebugMode) {
          print('⚠️ RevenueCat SDK not configured for current platform');
          print('   Please update API keys in RevenueCatConstants');
        }
        _isEnabled = false;
        return;
      }

      // Configure RevenueCat
      PurchasesConfiguration configuration = PurchasesConfiguration(RevenueCatConstants.API_KEY);

      // Initialize RevenueCat
      await Purchases.configure(configuration);

      // Set up listeners
      Purchases.addCustomerInfoUpdateListener(_onCustomerInfoUpdate);

      // Load initial data
      await _loadCustomerInfo();
      await _loadOfferings();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ RevenueCat SDK initialized successfully');
        print('   Has Premium: $hasPremiumAccess');
        print('   Is Trial: $isInTrial');
        print('   Status: $subscriptionStatus');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ RevenueCat SDK initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      _isEnabled = false;
      // Don't rethrow - app should continue even if RevenueCat fails
    }
  }

  /// Load customer info
  Future<void> _loadCustomerInfo() async {
    try {
      _customerInfo = await Purchases.getCustomerInfo();
      _customerInfoController.add(_customerInfo!);
      _subscriptionStatusController.add(hasPremiumAccess);
      
      if (kDebugMode) {
        print('📊 Customer info loaded:');
        print('   User ID: ${_customerInfo!.originalAppUserId}');
        print('   Active entitlements: ${_customerInfo!.entitlements.active.keys}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load customer info: $e');
      }
    }
  }

  /// Load offerings
  Future<void> _loadOfferings() async {
    try {
      _offerings = await Purchases.getOfferings();
      
      if (kDebugMode) {
        print('📦 Offerings loaded:');
        print('   Current offering: ${_offerings?.current?.identifier}');
        print('   Available packages: ${_offerings?.current?.availablePackages.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load offerings: $e');
      }
    }
  }

  /// Customer info update listener
  void _onCustomerInfoUpdate(CustomerInfo customerInfo) {
    _customerInfo = customerInfo;
    _customerInfoController.add(customerInfo);
    _subscriptionStatusController.add(hasPremiumAccess);
    
    if (kDebugMode) {
      print('🔄 Customer info updated:');
      print('   Has Premium: $hasPremiumAccess');
      print('   Is Trial: $isInTrial');
    }
  }

  /// Purchase a package
  Future<RevenueCatPurchaseResult> purchasePackage(Package package) async {
    if (!isEnabled) {
      return RevenueCatPurchaseResult.error('RevenueCat not enabled');
    }

    try {
      if (kDebugMode) {
        print('💳 Starting purchase: ${package.identifier}');
        print('   Product: ${package.storeProduct.identifier}');
        print('   Price: ${package.storeProduct.priceString}');
      }

      final purchaseResult = await Purchases.purchasePackage(package);

      if (kDebugMode) {
        print('✅ Purchase completed successfully');
        print('   Active entitlements: ${purchaseResult.customerInfo.entitlements.active.keys}');
      }

      return RevenueCatPurchaseResult.success(purchaseResult.customerInfo);

    } on PlatformException catch (e) {
      final errorCode = PurchasesErrorHelper.getErrorCode(e);

      if (kDebugMode) {
        print('❌ Purchase failed: ${e.message}');
        print('   Error code: $errorCode');
      }

      if (errorCode == PurchasesErrorCode.purchaseCancelledError) {
        return RevenueCatPurchaseResult.cancelled();
      } else {
        return RevenueCatPurchaseResult.error(e.message ?? 'Purchase failed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Unexpected purchase error: $e');
      }
      return RevenueCatPurchaseResult.error('Unexpected error occurred');
    }
  }

  /// Restore purchases
  Future<RestoreResult> restorePurchases() async {
    if (!isEnabled) {
      return RestoreResult.error('RevenueCat not enabled');
    }

    try {
      if (kDebugMode) {
        print('🔄 Restoring purchases...');
      }

      CustomerInfo customerInfo = await Purchases.restorePurchases();
      
      if (kDebugMode) {
        print('✅ Purchases restored');
        print('   Active entitlements: ${customerInfo.entitlements.active.keys}');
      }

      final hasActiveEntitlements = customerInfo.entitlements.active.isNotEmpty;
      return RestoreResult.success(hasActiveEntitlements);

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to restore purchases: $e');
      }
      return RestoreResult.error('Failed to restore purchases');
    }
  }

  /// Get current offering
  Offering? getCurrentOffering() {
    return _offerings?.current;
  }

  /// Get package by type
  Package? getPackage(String packageType) {
    final offering = getCurrentOffering();
    if (offering == null) return null;

    switch (packageType) {
      case RevenueCatConstants.PACKAGE_TYPE_WEEKLY:
        return offering.weekly;
      case RevenueCatConstants.PACKAGE_TYPE_MONTHLY:
        return offering.monthly;
      case RevenueCatConstants.PACKAGE_TYPE_ANNUAL:
        return offering.annual;
      case RevenueCatConstants.PACKAGE_TYPE_LIFETIME:
        return offering.lifetime;
      default:
        return null;
    }
  }

  /// Get all available packages
  List<Package> getAvailablePackages() {
    final offering = getCurrentOffering();
    return offering?.availablePackages ?? [];
  }

  /// Set user ID for analytics
  Future<void> setUserId(String userId) async {
    if (!isEnabled) return;

    try {
      await Purchases.logIn(userId);
      if (kDebugMode) {
        print('👤 User ID set: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set user ID: $e');
      }
    }
  }

  /// Set user attributes
  Future<void> setUserAttributes(Map<String, String> attributes) async {
    if (!isEnabled) return;

    try {
      await Purchases.setAttributes(attributes);
      if (kDebugMode) {
        print('📊 User attributes set: $attributes');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set user attributes: $e');
      }
    }
  }

  /// Get service status for debugging
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isEnabled': _isEnabled,
      'isConfigured': RevenueCatConstants.isConfigured,
      'platformSupported': Platform.isIOS || Platform.isAndroid,
      'hasPremiumAccess': hasPremiumAccess,
      'isInTrial': isInTrial,
      'subscriptionStatus': subscriptionStatus,
      'availablePackages': getAvailablePackages().length,
      'apiKey': RevenueCatConstants.API_KEY,
    };
  }

  /// Dispose resources
  void dispose() {
    _subscriptionStatusController.close();
    _customerInfoController.close();
    if (kDebugMode) {
      print('🧹 RevenueCat service disposed');
    }
  }
}

/// Purchase result wrapper
class RevenueCatPurchaseResult {
  final bool isSuccess;
  final bool isCancelled;
  final String? error;
  final CustomerInfo? customerInfo;

  RevenueCatPurchaseResult.success(this.customerInfo)
      : isSuccess = true, isCancelled = false, error = null;

  RevenueCatPurchaseResult.cancelled()
      : isSuccess = false, isCancelled = true, error = null, customerInfo = null;

  RevenueCatPurchaseResult.error(this.error)
      : isSuccess = false, isCancelled = false, customerInfo = null;
}

/// Restore result wrapper
class RestoreResult {
  final bool isSuccess;
  final bool hasActiveSubscriptions;
  final String? error;

  RestoreResult.success(this.hasActiveSubscriptions) 
      : isSuccess = true, error = null;
  
  RestoreResult.error(this.error) 
      : isSuccess = false, hasActiveSubscriptions = false;
}
