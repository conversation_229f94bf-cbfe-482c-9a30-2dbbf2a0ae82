// lib/services/purchase_service.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/constants/revenuecat_constants.dart';
import 'package:brainy_bunny/constants/adjust_constants.dart';
import 'package:brainy_bunny/constants/meta_constants.dart';
import 'package:brainy_bunny/services/revenuecat_service.dart';
import 'package:brainy_bunny/services/adjust_service.dart';
import 'package:brainy_bunny/services/meta_service.dart';

/// Unified purchase service that integrates RevenueCat with analytics SDKs
/// Handles subscription purchases, trials, and premium access checking
class PurchaseService {
  // Singleton pattern
  static final PurchaseService _instance = PurchaseService._internal();
  static PurchaseService get instance => _instance;
  PurchaseService._internal();

  // Services
  final RevenueCatService _revenueCatService = RevenueCatService.instance;
  final AdjustService _adjustService = AdjustService.instance;
  final MetaService _metaService = MetaService.instance;

  // State
  bool _isInitialized = false;
  StreamSubscription<bool>? _subscriptionStatusSubscription;

  // Stream controllers
  final StreamController<bool> _premiumStatusController = StreamController<bool>.broadcast();

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isPurchased => _revenueCatService.hasPremiumAccess;
  bool get isInTrial => _revenueCatService.isInTrial;
  String get subscriptionStatus => _revenueCatService.subscriptionStatus;
  
  // Streams
  Stream<bool> get premiumStatusStream => _premiumStatusController.stream;

  /// Get display price for the primary subscription
  String get displayPrice {
    final package = _revenueCatService.getPackage(RevenueCatConstants.PACKAGE_TYPE_MONTHLY);
    return package?.storeProduct.priceString ?? '\$9.99';
  }

  /// Initialize purchase service
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Purchase service already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 === PURCHASE SERVICE INITIALIZATION ===');
      }

      // Initialize RevenueCat service
      await _revenueCatService.initialize();

      // Set up subscription status listener
      _subscriptionStatusSubscription = _revenueCatService.subscriptionStatusStream.listen(
        (hasPremium) {
          _premiumStatusController.add(hasPremium);
          if (kDebugMode) {
            print('🔄 Premium status updated: $hasPremium');
          }
        },
      );

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Purchase service initialized successfully');
        print('   Has Premium: $isPurchased');
        print('   Is Trial: $isInTrial');
        print('   Status: $subscriptionStatus');
        print('   Display Price: $displayPrice');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Purchase service initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      // Don't rethrow - app should continue even if purchase service fails
    }
  }

  /// Check if a level is unlocked
  bool isLevelUnlocked(int levelIndex) {
    // Levels 0-4 (first 5 levels) are always free
    if (levelIndex < AppConstants.FREE_GAMES_COUNT) {
      return true;
    }
    
    // Premium levels require subscription
    return isPurchased;
  }

  /// Purchase a subscription package
  Future<RevenueCatPurchaseResult> purchasePackage(Package package) async {
    if (!_revenueCatService.isEnabled) {
      return RevenueCatPurchaseResult.error('Purchase service not available');
    }

    try {
      if (kDebugMode) {
        print('💳 Starting purchase: ${package.identifier}');
        print('   Product: ${package.storeProduct.identifier}');
        print('   Price: ${package.storeProduct.priceString}');
      }

      // Track purchase attempt
      await _adjustService.trackEvent(
        AdjustConstants.EVENT_PURCHASE_ATTEMPT,
        callbackParameters: {
          'product_id': package.storeProduct.identifier,
          'price': package.storeProduct.priceString,
          'currency': package.storeProduct.currencyCode ?? 'USD',
        },
      );

      // Track initiated checkout with Meta
      await _metaService.trackInitiatedCheckout(
        productId: package.storeProduct.identifier,
        value: package.storeProduct.price,
        currency: package.storeProduct.currencyCode ?? 'USD',
      );

      // Perform the purchase
      final result = await _revenueCatService.purchasePackage(package);

      if (result.isSuccess && result.customerInfo != null) {
        // Track successful purchase with Adjust
        await _adjustService.trackSubscriptionStart(
          productId: package.storeProduct.identifier,
          revenue: package.storeProduct.price,
          currency: package.storeProduct.currencyCode ?? 'USD',
          subscriptionType: _getSubscriptionType(package),
        );

        // Track purchase with Meta
        await _metaService.trackPurchase(
          productId: package.storeProduct.identifier,
          amount: package.storeProduct.price,
          currency: package.storeProduct.currencyCode ?? 'USD',
          paymentMethod: Platform.isIOS ? 'app_store' : 'google_play',
        );

        // Track Subscribe event (Facebook Standard Event)
        await _metaService.trackSubscribe(
          productId: package.storeProduct.identifier,
          value: package.storeProduct.price,
          currency: package.storeProduct.currencyCode ?? 'USD',
          subscriptionType: _getSubscriptionType(package),
        );

        if (kDebugMode) {
          print('✅ Purchase completed successfully');
        }

        return RevenueCatPurchaseResult.success(result.customerInfo!);

      } else if (result.isCancelled) {
        if (kDebugMode) {
          print('❌ Purchase cancelled by user');
        }
        return RevenueCatPurchaseResult.cancelled();

      } else {
        if (kDebugMode) {
          print('❌ Purchase failed: ${result.error}');
        }
        return RevenueCatPurchaseResult.error(result.error ?? 'Purchase failed');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Unexpected purchase error: $e');
      }
      return RevenueCatPurchaseResult.error('Unexpected error occurred');
    }
  }

  /// Restore purchases
  Future<RestoreResult> restorePurchases() async {
    if (!_revenueCatService.isEnabled) {
      return RestoreResult.error('Purchase service not available');
    }

    try {
      if (kDebugMode) {
        print('🔄 Restoring purchases...');
      }

      final result = await _revenueCatService.restorePurchases();

      if (result.isSuccess) {
        if (kDebugMode) {
          print('✅ Purchases restored successfully');
          print('   Has active subscriptions: ${result.hasActiveSubscriptions}');
        }

        return RestoreResult.success(result.hasActiveSubscriptions);
      } else {
        if (kDebugMode) {
          print('❌ Failed to restore purchases: ${result.error}');
        }
        return RestoreResult.error(result.error ?? 'Failed to restore purchases');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Unexpected restore error: $e');
      }
      return RestoreResult.error('Unexpected error occurred');
    }
  }

  /// Get available subscription packages
  List<Package> getAvailablePackages() {
    return _revenueCatService.getAvailablePackages();
  }

  /// Get current offering
  Offering? getCurrentOffering() {
    return _revenueCatService.getCurrentOffering();
  }

  /// Get package by type
  Package? getPackage(String packageType) {
    return _revenueCatService.getPackage(packageType);
  }

  /// Track trial start (when user begins playing)
  Future<void> trackTrialStart() async {
    await _adjustService.trackTrialStart();
    await _metaService.trackStartTrial(
      trialType: MetaConstants.TRIAL_TYPES['free_levels'],
      value: 0.0,
      currency: 'USD',
    );
  }

  /// Track tutorial completion (when user finishes level 5)
  Future<void> trackTutorialComplete() async {
    await _adjustService.trackTutorialComplete();
    await _metaService.trackCompletedTutorial(
      contentId: 'onboarding_levels_1_to_${AppConstants.FREE_GAMES_COUNT}',
      success: true,
    );
  }

  /// Track trial completion (when user finishes all free levels)
  Future<void> trackTrialComplete() async {
    await _adjustService.trackTrialComplete();
    await _metaService.trackTrialCompleted(
      freeLevelsCount: AppConstants.FREE_GAMES_COUNT,
      totalLevelsCount: AppConstants.TOTAL_GAMES_COUNT,
    );
  }

  /// Helper method to determine subscription type
  String _getSubscriptionType(Package package) {
    if (package.packageType == PackageType.weekly) {
      return RevenueCatConstants.SUBSCRIPTION_TYPES['weekly'] ?? 'weekly';
    } else if (package.packageType == PackageType.monthly) {
      return RevenueCatConstants.SUBSCRIPTION_TYPES['monthly'] ?? 'monthly';
    } else if (package.packageType == PackageType.annual) {
      return RevenueCatConstants.SUBSCRIPTION_TYPES['yearly'] ?? 'yearly';
    } else if (package.packageType == PackageType.lifetime) {
      return RevenueCatConstants.SUBSCRIPTION_TYPES['lifetime'] ?? 'lifetime';
    } else {
      return 'unknown';
    }
  }

  /// Get service status for debugging
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isPurchased': isPurchased,
      'isInTrial': isInTrial,
      'subscriptionStatus': subscriptionStatus,
      'displayPrice': displayPrice,
      'availablePackages': getAvailablePackages().length,
      'revenueCatStatus': _revenueCatService.getServiceStatus(),
    };
  }

  /// Dispose resources
  void dispose() {
    _subscriptionStatusSubscription?.cancel();
    _premiumStatusController.close();
    _revenueCatService.dispose();
    if (kDebugMode) {
      print('🧹 Purchase service disposed');
    }
  }
}



/// Restore result wrapper
class RestoreResult {
  final bool isSuccess;
  final bool hasActiveSubscriptions;
  final String? error;

  RestoreResult.success(this.hasActiveSubscriptions) 
      : isSuccess = true, error = null;
  
  RestoreResult.error(this.error) 
      : isSuccess = false, hasActiveSubscriptions = false;
}
