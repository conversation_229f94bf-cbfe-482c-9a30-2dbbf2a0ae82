// lib/services/adjust_service.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:adjust_sdk/adjust.dart';
import 'package:adjust_sdk/adjust_config.dart';
import 'package:adjust_sdk/adjust_event.dart';
import 'package:adjust_sdk/adjust_attribution.dart';
import 'package:brainy_bunny/constants/adjust_constants.dart';

/// Adjust SDK service for attribution tracking and analytics
/// Tracks user acquisition, engagement, and revenue events
class AdjustService {
  // Singleton pattern
  static final AdjustService _instance = AdjustService._internal();
  static AdjustService get instance => _instance;
  AdjustService._internal();

  // State
  bool _isInitialized = false;
  bool _isEnabled = true;
  AdjustAttribution? _attribution;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled && AdjustConstants.isConfiguredForCurrentPlatform;
  String get appToken => AdjustConstants.APP_TOKEN;
  AdjustAttribution? get attribution => _attribution;

  /// Initialize Adjust SDK
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 === ADJUST SDK INITIALIZATION ===');
        print('   Platform: ${Platform.operatingSystem}');
        print('   App Token: ${AdjustConstants.APP_TOKEN}');
        print('   Environment: ${AdjustConstants.IS_PRODUCTION ? "Production" : "Sandbox"}');
      }

      // Check if Adjust SDK is configured
      if (!AdjustConstants.isConfiguredForCurrentPlatform) {
        if (kDebugMode) {
          print('⚠️ Adjust SDK not configured for current platform');
          print('   Please update APP_TOKEN in AdjustConstants');
        }
        _isEnabled = false;
        return;
      }

      // Create Adjust configuration
      AdjustEnvironment environment = AdjustConstants.IS_PRODUCTION
          ? AdjustEnvironment.production
          : AdjustEnvironment.sandbox;

      AdjustConfig config = AdjustConfig(AdjustConstants.APP_TOKEN, environment);
      
      // Configure logging level
      config.logLevel = kDebugMode ? AdjustLogLevel.verbose : AdjustLogLevel.warn;
      
      // Set attribution callback
      config.attributionCallback = (AdjustAttribution attribution) {
        _attribution = attribution;
        if (kDebugMode) {
          print('📊 Adjust Attribution received:');
          print('   Network: ${attribution.network}');
          print('   Campaign: ${attribution.campaign}');
          print('   Adgroup: ${attribution.adgroup}');
          print('   Creative: ${attribution.creative}');
        }
      };
      
      // Initialize the SDK
      Adjust.initSdk(config);
      
      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Adjust SDK initialized successfully');
        print('   SKAN Postbacks: ${AdjustConstants.SKAN_POSTBACK_URL}');
      }

      // Track app launch
      await trackAppLaunch();

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Adjust SDK initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      _isEnabled = false;
      // Don't rethrow - app should continue even if Adjust fails
    }
  }

  /// Track standard app events
  Future<void> trackAppLaunch() async {
    await trackEvent(
      AdjustConstants.EVENT_APP_LAUNCH,
      callbackParameters: {
        'platform': Platform.operatingSystem,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track game events
  Future<void> trackGameStart(int gameIndex, {String? gameType}) async {
    await trackEvent(
      AdjustConstants.EVENT_GAME_START,
      callbackParameters: {
        AdjustConstants.PARAM_LEVEL: gameIndex.toString(),
        AdjustConstants.PARAM_GAME_TYPE: gameType ?? AdjustConstants.GAME_TYPES[gameIndex + 1] ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackGameComplete(int gameIndex, {
    String? gameType,
    int? score,
    int? duration,
    bool? success,
  }) async {
    final parameters = <String, String>{
      AdjustConstants.PARAM_LEVEL: gameIndex.toString(),
      AdjustConstants.PARAM_GAME_TYPE: gameType ?? AdjustConstants.GAME_TYPES[gameIndex + 1] ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (score != null) parameters[AdjustConstants.PARAM_SCORE] = score.toString();
    if (duration != null) parameters[AdjustConstants.PARAM_DURATION] = duration.toString();
    if (success != null) parameters[AdjustConstants.PARAM_SUCCESS] = success.toString();

    await trackEvent(AdjustConstants.EVENT_GAME_COMPLETE, callbackParameters: parameters);
  }

  Future<void> trackLevelComplete(int level, {bool? success}) async {
    await trackEvent(
      AdjustConstants.EVENT_LEVEL_COMPLETE,
      callbackParameters: {
        AdjustConstants.PARAM_LEVEL: level.toString(),
        AdjustConstants.PARAM_SUCCESS: (success ?? true).toString(),
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track tutorial and trial events
  Future<void> trackTutorialComplete() async {
    await trackEvent(
      AdjustConstants.EVENT_TUTORIAL_COMPLETE,
      callbackParameters: {
        'completed_levels': '5',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackTrialStart() async {
    await trackEvent(
      AdjustConstants.EVENT_TRIAL_START,
      callbackParameters: {
        AdjustConstants.PARAM_TRIAL_PERIOD: 'free_levels_1_to_5',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackTrialComplete() async {
    await trackEvent(
      AdjustConstants.EVENT_TRIAL_COMPLETE,
      callbackParameters: {
        'completed_levels': '5',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Track paywall and purchase events
  Future<void> trackPaywallView({String? triggeredFrom, int? levelIndex}) async {
    final parameters = <String, String>{
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (triggeredFrom != null) parameters[AdjustConstants.PARAM_TRIGGERED_FROM] = triggeredFrom;
    if (levelIndex != null) parameters[AdjustConstants.PARAM_LEVEL] = levelIndex.toString();

    await trackEvent(AdjustConstants.EVENT_PAYWALL_VIEW, callbackParameters: parameters);
  }

  Future<void> trackParentalGateView({String? triggeredFrom}) async {
    await trackEvent(
      AdjustConstants.EVENT_PARENTAL_GATE_VIEW,
      callbackParameters: {
        AdjustConstants.PARAM_TRIGGERED_FROM: triggeredFrom ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackParentalGateSuccess({String? triggeredFrom, int? attempts}) async {
    final parameters = <String, String>{
      AdjustConstants.PARAM_TRIGGERED_FROM: triggeredFrom ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (attempts != null) parameters[AdjustConstants.PARAM_ATTEMPTS] = attempts.toString();

    await trackEvent(AdjustConstants.EVENT_PARENTAL_GATE_SUCCESS, callbackParameters: parameters);
  }

  /// Track subscription events with revenue
  Future<void> trackSubscriptionStart({
    required String productId,
    required double revenue,
    required String currency,
    String? subscriptionType,
  }) async {
    await trackEvent(
      AdjustConstants.EVENT_SUBSCRIPTION_START,
      revenue: revenue,
      currency: currency,
      callbackParameters: {
        AdjustConstants.PARAM_PRODUCT_ID: productId,
        AdjustConstants.PARAM_SUBSCRIPTION_TYPE: subscriptionType ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Generic event tracking method
  Future<void> trackEvent(
    String eventToken, {
    double? revenue,
    String? currency,
    Map<String, String>? callbackParameters,
    Map<String, String>? partnerParameters,
  }) async {
    if (!isEnabled) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK not enabled - skipping event: $eventToken');
      }
      return;
    }

    try {
      AdjustEvent adjustEvent = AdjustEvent(eventToken);

      // Add revenue if provided
      if (revenue != null && currency != null) {
        adjustEvent.setRevenue(revenue, currency);
      }

      // Add callback parameters
      if (callbackParameters != null) {
        callbackParameters.forEach((key, value) {
          adjustEvent.addCallbackParameter(key, value);
        });
      }

      // Add partner parameters
      if (partnerParameters != null) {
        partnerParameters.forEach((key, value) {
          adjustEvent.addPartnerParameter(key, value);
        });
      }

      // Track the event
      Adjust.trackEvent(adjustEvent);

      if (kDebugMode) {
        print('📊 Adjust Event: $eventToken');
        if (revenue != null) print('   Revenue: $revenue $currency');
        if (callbackParameters != null) print('   Parameters: $callbackParameters');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track Adjust event $eventToken: $e');
      }
    }
  }

  /// Get service status for debugging
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isEnabled': _isEnabled,
      'isConfigured': AdjustConstants.isConfigured,
      'platformSupported': Platform.isIOS || Platform.isAndroid,
      'appToken': AdjustConstants.APP_TOKEN,
      'environment': AdjustConstants.IS_PRODUCTION ? 'Production' : 'Sandbox',
      'attribution': _attribution != null ? {
        'network': _attribution!.network,
        'campaign': _attribution!.campaign,
        'adgroup': _attribution!.adgroup,
        'creative': _attribution!.creative,
      } : null,
    };
  }

  /// Enable/disable Adjust SDK
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (_isInitialized) {
      Adjust.switchToOfflineMode();
    }
    if (kDebugMode) {
      print('📊 Adjust SDK ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// GDPR compliance - forget user
  void gdprForgetMe() {
    if (_isInitialized) {
      Adjust.gdprForgetMe();
      if (kDebugMode) {
        print('🔒 Adjust GDPR forget me called');
      }
    }
  }
}
