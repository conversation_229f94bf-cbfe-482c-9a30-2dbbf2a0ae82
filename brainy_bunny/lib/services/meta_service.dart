// lib/services/meta_service.dart
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:brainy_bunny/constants/meta_constants.dart';

/// Meta SDK (Facebook SDK) service for analytics and app events
/// Tracks user engagement, game progress, and subscription events
class MetaService {
  // Singleton pattern
  static final MetaService _instance = MetaService._internal();
  static MetaService get instance => _instance;
  MetaService._internal();

  // Facebook App Events instance
  static final FacebookAppEvents _facebookAppEvents = FacebookAppEvents();
  
  // State
  bool _isInitialized = false;
  bool _isEnabled = true;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isEnabled => _isEnabled && MetaConstants.isConfiguredForCurrentPlatform;

  /// Initialize Meta SDK
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Meta SDK already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🚀 === META SDK INITIALIZATION ===');
        print('   Platform: ${Platform.operatingSystem}');
        print('   Configured: ${MetaConstants.isConfigured}');
        print('   App ID: ${MetaConstants.FACEBOOK_APP_ID}');
      }

      // Check if Meta SDK is configured
      if (!MetaConstants.isConfiguredForCurrentPlatform) {
        if (kDebugMode) {
          print('⚠️ Meta SDK not configured for current platform');
          print('   App ID and Client Token are already configured');
        }
        _isEnabled = false;
        return;
      }

      // Initialize Facebook App Events
      await _facebookAppEvents.setAutoLogAppEventsEnabled(MetaConstants.AUTO_LOG_APP_EVENTS);
      // Note: Advertiser ID collection is configured in platform-specific files

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Meta SDK initialized successfully');
        print('   Auto log events: ${MetaConstants.AUTO_LOG_APP_EVENTS}');
        print('   Advertiser ID collection: ${MetaConstants.ADVERTISER_ID_COLLECTION}');
      }

      // Track app launch
      await trackAppLaunched();

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Meta SDK initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      _isEnabled = false;
      // Don't rethrow - app should continue even if Meta SDK fails
    }
  }

  /// Track Facebook Standard Events for user journey
  Future<void> trackCompletedTutorial({
    String? contentId,
    bool? success,
  }) async {
    final parameters = <String, dynamic>{};

    if (contentId != null) parameters[MetaConstants.PARAM_CONTENT_ID] = contentId;
    if (success != null) parameters[MetaConstants.PARAM_SUCCESS] = success;

    await _trackEvent(MetaConstants.EVENT_COMPLETED_TUTORIAL, parameters);
  }

  Future<void> trackStartTrial({
    String? trialType,
    double? value,
    String? currency,
  }) async {
    final parameters = <String, dynamic>{};

    if (trialType != null) parameters[MetaConstants.PARAM_TRIAL_PERIOD] = trialType;
    if (value != null) parameters[MetaConstants.PARAM_VALUE] = value;
    if (currency != null) parameters[MetaConstants.PARAM_CURRENCY] = currency;

    await _trackEvent(MetaConstants.EVENT_START_TRIAL, parameters);
  }

  Future<void> trackSubscribe({
    required String productId,
    required double value,
    required String currency,
    String? subscriptionType,
  }) async {
    final parameters = <String, dynamic>{
      MetaConstants.PARAM_VALUE: value,
      MetaConstants.PARAM_CURRENCY: currency,
      MetaConstants.PARAM_CONTENT_ID: productId,
      MetaConstants.PARAM_CONTENT_TYPE: MetaConstants.CONTENT_TYPE_PRODUCT,
    };

    if (subscriptionType != null) parameters[MetaConstants.PARAM_SUBSCRIPTION_TYPE] = subscriptionType;

    await _trackEvent(MetaConstants.EVENT_SUBSCRIBE, parameters);
  }

  /// Track standard app events
  Future<void> trackAppLaunched() async {
    await _trackEvent(MetaConstants.EVENT_APP_LAUNCHED);
  }

  Future<void> trackAppInstalled() async {
    await _trackEvent(MetaConstants.EVENT_APP_INSTALLED);
  }

  Future<void> trackSessionStart() async {
    await _trackEvent(MetaConstants.EVENT_SESSION_START);
  }

  /// Track game progress events
  Future<void> trackGameStarted(int gameIndex, {
    String? gameType,
    String? difficulty,
    String? ageGroup,
  }) async {
    final parameters = <String, dynamic>{
      MetaConstants.PARAM_LEVEL: gameIndex,
      MetaConstants.PARAM_CONTENT_TYPE: MetaConstants.CONTENT_TYPE_PRODUCT,
      MetaConstants.PARAM_CONTENT_ID: 'game_$gameIndex',
    };

    if (gameType != null) parameters[MetaConstants.PARAM_GAME_TYPE] = gameType;
    if (difficulty != null) parameters[MetaConstants.PARAM_DIFFICULTY] = difficulty;
    if (ageGroup != null) parameters[MetaConstants.PARAM_AGE_GROUP] = ageGroup;

    await _trackEvent(MetaConstants.EVENT_GAME_STARTED, parameters);
  }

  Future<void> trackGameCompleted(int gameIndex, {
    String? gameType,
    int? score,
    int? duration,
    bool? success,
    int? attempts,
    int? hintsUsed,
  }) async {
    final parameters = <String, dynamic>{
      MetaConstants.PARAM_LEVEL: gameIndex,
      MetaConstants.PARAM_CONTENT_TYPE: MetaConstants.CONTENT_TYPE_PRODUCT,
      MetaConstants.PARAM_CONTENT_ID: 'game_$gameIndex',
    };

    if (gameType != null) parameters[MetaConstants.PARAM_GAME_TYPE] = gameType;
    if (score != null) parameters[MetaConstants.PARAM_SCORE] = score;
    if (duration != null) parameters[MetaConstants.PARAM_DURATION] = duration;
    if (success != null) parameters[MetaConstants.PARAM_SUCCESS] = success;
    if (attempts != null) parameters[MetaConstants.PARAM_ATTEMPTS] = attempts;
    if (hintsUsed != null) parameters[MetaConstants.PARAM_HINTS_USED] = hintsUsed;

    await _trackEvent(MetaConstants.EVENT_GAME_COMPLETED, parameters);
  }

  Future<void> trackLevelAchieved(int level, {
    String? gameType,
    int? score,
  }) async {
    final parameters = <String, dynamic>{
      MetaConstants.PARAM_LEVEL: level,
    };

    if (gameType != null) parameters[MetaConstants.PARAM_GAME_TYPE] = gameType;
    if (score != null) parameters[MetaConstants.PARAM_SCORE] = score;

    await _trackEvent(MetaConstants.EVENT_LEVEL_ACHIEVED, parameters);
  }

  /// Track purchase events
  Future<void> trackPurchase({
    required String productId,
    required double amount,
    required String currency,
    String? transactionId,
    String? paymentMethod,
  }) async {
    final parameters = <String, dynamic>{
      MetaConstants.PARAM_CURRENCY: currency,
      MetaConstants.PARAM_VALUE: amount,
      MetaConstants.PARAM_CONTENT_TYPE: MetaConstants.CONTENT_TYPE_PRODUCT,
      MetaConstants.PARAM_CONTENT_ID: productId,
      MetaConstants.PARAM_PRODUCT_ID: productId,
      MetaConstants.PARAM_PURCHASE_TYPE: MetaConstants.PURCHASE_TYPE_SUBSCRIPTION,
    };

    if (transactionId != null) parameters[MetaConstants.PARAM_TRANSACTION_ID] = transactionId;
    if (paymentMethod != null) parameters[MetaConstants.PARAM_PAYMENT_METHOD] = paymentMethod;

    await _trackEvent(MetaConstants.EVENT_PURCHASE, parameters);
  }

  Future<void> trackAddToCart(String productId, {
    double? value,
    String? currency,
  }) async {
    final parameters = <String, dynamic>{
      MetaConstants.PARAM_CONTENT_TYPE: MetaConstants.CONTENT_TYPE_PRODUCT,
      MetaConstants.PARAM_CONTENT_ID: productId,
    };

    if (value != null) parameters[MetaConstants.PARAM_VALUE] = value;
    if (currency != null) parameters[MetaConstants.PARAM_CURRENCY] = currency;

    await _trackEvent(MetaConstants.EVENT_ADD_TO_CART, parameters);
  }

  Future<void> trackInitiatedCheckout({
    String? productId,
    double? value,
    String? currency,
  }) async {
    final parameters = <String, dynamic>{};

    if (productId != null) {
      parameters[MetaConstants.PARAM_CONTENT_TYPE] = MetaConstants.CONTENT_TYPE_PRODUCT;
      parameters[MetaConstants.PARAM_CONTENT_ID] = productId;
    }
    if (value != null) parameters[MetaConstants.PARAM_VALUE] = value;
    if (currency != null) parameters[MetaConstants.PARAM_CURRENCY] = currency;

    await _trackEvent(MetaConstants.EVENT_INITIATED_CHECKOUT, parameters);
  }

  /// Track custom Brainy Bunny events
  Future<void> trackPaywallViewed({
    String? triggeredFrom,
    int? levelIndex,
  }) async {
    final parameters = <String, dynamic>{};

    if (triggeredFrom != null) parameters[MetaConstants.PARAM_TRIGGERED_FROM] = triggeredFrom;
    if (levelIndex != null) parameters[MetaConstants.PARAM_LEVEL] = levelIndex;

    await _trackEvent(MetaConstants.EVENT_PAYWALL_VIEWED, parameters);
  }

  Future<void> trackParentalGateViewed({
    String? triggeredFrom,
    String? questionType,
  }) async {
    final parameters = <String, dynamic>{};

    if (triggeredFrom != null) parameters[MetaConstants.PARAM_TRIGGERED_FROM] = triggeredFrom;
    if (questionType != null) parameters['question_type'] = questionType;

    await _trackEvent(MetaConstants.EVENT_PARENTAL_GATE_VIEWED, parameters);
  }

  Future<void> trackParentalGatePassed({
    String? triggeredFrom,
    int? attempts,
  }) async {
    final parameters = <String, dynamic>{};

    if (triggeredFrom != null) parameters[MetaConstants.PARAM_TRIGGERED_FROM] = triggeredFrom;
    if (attempts != null) parameters[MetaConstants.PARAM_ATTEMPTS] = attempts;

    await _trackEvent(MetaConstants.EVENT_PARENTAL_GATE_PASSED, parameters);
  }

  Future<void> trackTrialCompleted({
    int? freeLevelsCount,
    int? totalLevelsCount,
  }) async {
    final parameters = <String, dynamic>{};

    if (freeLevelsCount != null) parameters['free_levels_count'] = freeLevelsCount;
    if (totalLevelsCount != null) parameters['total_levels_count'] = totalLevelsCount;

    await _trackEvent(MetaConstants.EVENT_TRIAL_COMPLETED, parameters);
  }

  Future<void> trackPremiumUnlocked({
    String? method,
    String? productId,
  }) async {
    final parameters = <String, dynamic>{};

    if (method != null) parameters['unlock_method'] = method;
    if (productId != null) parameters[MetaConstants.PARAM_PRODUCT_ID] = productId;

    await _trackEvent(MetaConstants.EVENT_PREMIUM_UNLOCKED, parameters);
  }

  /// Track custom event with parameters
  Future<void> trackCustomEvent(String eventName, [Map<String, dynamic>? parameters]) async {
    await _trackEvent(eventName, parameters);
  }

  /// Internal method to track events
  Future<void> _trackEvent(String eventName, [Map<String, dynamic>? parameters]) async {
    if (!isEnabled) {
      if (kDebugMode) {
        print('⚠️ Meta SDK not enabled - skipping event: $eventName');
      }
      return;
    }

    try {
      if (parameters != null && parameters.isNotEmpty) {
        await _facebookAppEvents.logEvent(
          name: eventName,
          parameters: parameters,
        );
      } else {
        await _facebookAppEvents.logEvent(name: eventName);
      }

      if (kDebugMode) {
        print('📊 Meta Event: $eventName');
        if (parameters != null && parameters.isNotEmpty) {
          print('   Parameters: $parameters');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track Meta event $eventName: $e');
      }
    }
  }

  /// Get service status for debugging
  Map<String, dynamic> getServiceStatus() {
    return {
      'isInitialized': _isInitialized,
      'isEnabled': _isEnabled,
      'isConfigured': MetaConstants.isConfigured,
      'platformSupported': Platform.isIOS || Platform.isAndroid,
      'autoLogEvents': MetaConstants.AUTO_LOG_APP_EVENTS,
      'advertiserIdCollection': MetaConstants.ADVERTISER_ID_COLLECTION,
      'facebookAppId': MetaConstants.FACEBOOK_APP_ID,
    };
  }

  /// Enable/disable Meta SDK
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (kDebugMode) {
      print('📊 Meta SDK ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Dispose resources
  void dispose() {
    // Facebook App Events doesn't require explicit disposal
    if (kDebugMode) {
      print('🧹 Meta SDK disposed');
    }
  }
}
