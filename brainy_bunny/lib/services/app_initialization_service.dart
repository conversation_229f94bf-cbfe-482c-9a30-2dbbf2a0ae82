// lib/services/app_initialization_service.dart
import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/firebase_options.dart';
import 'package:brainy_bunny/services/auth_service.dart';
import 'package:brainy_bunny/services/adjust_service.dart';
import 'package:brainy_bunny/services/revenuecat_service.dart';
import 'package:brainy_bunny/services/meta_service.dart';
import 'package:brainy_bunny/services/purchase_service.dart';

/// App initialization service that coordinates SDK initialization
/// Ensures all services are properly initialized in the correct order
class AppInitializationService {
  // Singleton pattern
  static final AppInitializationService _instance = AppInitializationService._internal();
  static AppInitializationService get instance => _instance;
  AppInitializationService._internal();

  // State
  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _initializationError;
  final List<String> _initializationLog = [];
  final Completer<void> _initializationCompleter = Completer<void>();

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  String? get initializationError => _initializationError;
  List<String> get initializationLog => List.unmodifiable(_initializationLog);
  Future<void> get initializationComplete => _initializationCompleter.future;

  /// Initialize all app services
  Future<void> initialize() async {
    if (_isInitialized || _isInitializing) {
      if (kDebugMode) {
        print('⚠️ App initialization already ${_isInitialized ? 'completed' : 'in progress'}');
      }
      return _initializationCompleter.future;
    }

    _isInitializing = true;
    _initializationLog.clear();
    _initializationError = null;

    try {
      if (kDebugMode) {
        print('🚀 === APP INITIALIZATION STARTED ===');
      }

      _log('App initialization started');

      // Step 1: Initialize Firebase
      await _initializeFirebase();

      // Step 2: Initialize AuthService
      await _initializeAuthService();

      // Step 3: Initialize Adjust SDK (Attribution tracking)
      await _initializeAdjustSDK();

      // Step 4: Initialize Meta SDK (Facebook Analytics)
      await _initializeMetaSDK();

      // Step 5: Initialize RevenueCat SDK (Subscription management)
      await _initializeRevenueCatSDK();

      // Step 6: Initialize Purchase Service (Unified purchase handling)
      await _initializePurchaseService();

      // Step 7: Track initial app events
      await _trackInitialEvents();

      _isInitialized = true;
      _isInitializing = false;

      _log('App initialization completed successfully');

      if (kDebugMode) {
        print('✅ === APP INITIALIZATION COMPLETED ===');
        _printInitializationSummary();
      }

      _initializationCompleter.complete();

    } catch (e, stackTrace) {
      _isInitializing = false;
      _initializationError = e.toString();
      _log('App initialization failed: $e');

      if (kDebugMode) {
        print('❌ === APP INITIALIZATION FAILED ===');
        print('Error: $e');
        print('Stack trace: $stackTrace');
        _printInitializationSummary();
      }

      _initializationCompleter.completeError(e, stackTrace);
    }
  }

  /// Initialize Firebase
  Future<void> _initializeFirebase() async {
    try {
      if (kDebugMode) {
        print('🔥 Initializing Firebase...');
      }

      await Firebase.initializeApp(
        name: 'brainy_bunny',
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Set named app as default instance for convenience
      Firebase.app('brainy_bunny').setAutomaticDataCollectionEnabled(true);

      if (kDebugMode) {
        print('✅ Firebase initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize AuthService
  Future<void> _initializeAuthService() async {
    try {
      if (kDebugMode) {
        print('🔐 Initializing AuthService...');
      }

      await AuthService.instance.initialize();

      if (kDebugMode) {
        print('✅ AuthService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ AuthService initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize Adjust SDK
  Future<void> _initializeAdjustSDK() async {
    try {
      _log('Initializing Adjust SDK...');

      final stopwatch = Stopwatch()..start();
      await AdjustService.instance.initialize();
      stopwatch.stop();

      final status = AdjustService.instance.getServiceStatus();
      _log('Adjust SDK initialized in ${stopwatch.elapsedMilliseconds}ms - Status: ${status['isEnabled'] ? 'Enabled' : 'Disabled'}');

      if (kDebugMode) {
        print('📊 Adjust SDK Status: $status');
      }

    } catch (e) {
      _log('Adjust SDK initialization failed: $e');
      if (kDebugMode) {
        print('❌ Adjust SDK initialization failed: $e');
      }
      // Don't rethrow - continue with other services
    }
  }

  /// Initialize Meta SDK
  Future<void> _initializeMetaSDK() async {
    try {
      _log('Initializing Meta SDK...');

      final stopwatch = Stopwatch()..start();
      await MetaService.instance.initialize();
      stopwatch.stop();

      final status = MetaService.instance.getServiceStatus();
      _log('Meta SDK initialized in ${stopwatch.elapsedMilliseconds}ms - Status: ${status['isEnabled'] ? 'Enabled' : 'Disabled'}');

      if (kDebugMode) {
        print('📊 Meta SDK Status: $status');
      }

    } catch (e) {
      _log('Meta SDK initialization failed: $e');
      if (kDebugMode) {
        print('❌ Meta SDK initialization failed: $e');
      }
      // Don't rethrow - continue with other services
    }
  }

  /// Initialize RevenueCat SDK
  Future<void> _initializeRevenueCatSDK() async {
    try {
      _log('Initializing RevenueCat SDK...');

      final stopwatch = Stopwatch()..start();
      await RevenueCatService.instance.initialize();
      stopwatch.stop();

      final status = RevenueCatService.instance.getServiceStatus();
      _log('RevenueCat SDK initialized in ${stopwatch.elapsedMilliseconds}ms - Status: ${status['isEnabled'] ? 'Enabled' : 'Disabled'}');

      if (kDebugMode) {
        print('📊 RevenueCat SDK Status: $status');
      }

    } catch (e) {
      _log('RevenueCat SDK initialization failed: $e');
      if (kDebugMode) {
        print('❌ RevenueCat SDK initialization failed: $e');
      }
      // Don't rethrow - continue with other services
    }
  }

  /// Initialize Purchase Service
  Future<void> _initializePurchaseService() async {
    try {
      _log('Initializing Purchase Service...');

      final stopwatch = Stopwatch()..start();
      await PurchaseService.instance.initialize();
      stopwatch.stop();

      final status = PurchaseService.instance.getServiceStatus();
      _log('Purchase Service initialized in ${stopwatch.elapsedMilliseconds}ms - Premium: ${status['isPurchased']}');

      if (kDebugMode) {
        print('📊 Purchase Service Status: $status');
      }

    } catch (e) {
      _log('Purchase Service initialization failed: $e');
      if (kDebugMode) {
        print('❌ Purchase Service initialization failed: $e');
      }
      // Don't rethrow - continue with other services
    }
  }

  /// Track initial app events
  Future<void> _trackInitialEvents() async {
    try {
      _log('Tracking initial app events...');

      // Track app launch with Adjust
      if (AdjustService.instance.isEnabled) {
        await AdjustService.instance.trackAppLaunch();
      }

      // Track app launch with Meta
      if (MetaService.instance.isEnabled) {
        await MetaService.instance.trackAppLaunched();
        await MetaService.instance.trackSessionStart();
      }

      // Track trial start if user is new
      if (PurchaseService.instance.isInitialized && !PurchaseService.instance.isPurchased) {
        await PurchaseService.instance.trackTrialStart();
      }

      _log('Initial app events tracked successfully');

    } catch (e) {
      _log('Failed to track initial events: $e');
      if (kDebugMode) {
        print('❌ Failed to track initial events: $e');
      }
      // Don't rethrow - this is not critical
    }
  }

  /// Log initialization step
  void _log(String message) {
    final timestamp = DateTime.now().toIso8601String();
    _initializationLog.add('[$timestamp] $message');

    if (kDebugMode) {
      print('🔧 $message');
    }
  }

  /// Print initialization summary
  void _printInitializationSummary() {
    if (!kDebugMode) return;

    print('\n📋 === INITIALIZATION SUMMARY ===');

    // Service statuses
    final adjustStatus = AdjustService.instance.getServiceStatus();
    final metaStatus = MetaService.instance.getServiceStatus();
    final revenueCatStatus = RevenueCatService.instance.getServiceStatus();
    final purchaseStatus = PurchaseService.instance.getServiceStatus();

    print('📊 Adjust SDK: ${adjustStatus['isEnabled'] ? '✅ Enabled' : '❌ Disabled'} (${adjustStatus['isConfigured'] ? 'Configured' : 'Not Configured'})');
    print('📊 Meta SDK: ${metaStatus['isEnabled'] ? '✅ Enabled' : '❌ Disabled'} (${metaStatus['isConfigured'] ? 'Configured' : 'Not Configured'})');
    print('📊 RevenueCat SDK: ${revenueCatStatus['isEnabled'] ? '✅ Enabled' : '❌ Disabled'} (${revenueCatStatus['isConfigured'] ? 'Configured' : 'Not Configured'})');
    print('📊 Purchase Service: ${purchaseStatus['isInitialized'] ? '✅ Initialized' : '❌ Not Initialized'}');

    // User status
    print('👤 User Status:');
    print('   Premium: ${purchaseStatus['isPurchased'] ? '✅ Yes' : '❌ No'}');
    print('   Trial: ${purchaseStatus['isInTrial'] ? '✅ Yes' : '❌ No'}');
    print('   Status: ${purchaseStatus['subscriptionStatus']}');

    // Configuration warnings
    print('\n⚠️  Configuration Notes:');
    if (!adjustStatus['isConfigured']) {
      print('   • Adjust SDK: Update APP_TOKEN in AdjustConstants');
    }
    if (!metaStatus['isConfigured']) {
      print('   • Meta SDK: Already configured with your credentials');
    }
    if (!revenueCatStatus['isConfigured']) {
      print('   • RevenueCat SDK: Update API keys in RevenueCatConstants');
    }

    print('\n📝 Initialization Log:');
    for (final logEntry in _initializationLog) {
      print('   $logEntry');
    }

    print('=================================\n');
  }

  /// Get overall service health
  Map<String, dynamic> getServiceHealth() {
    return {
      'isInitialized': _isInitialized,
      'isInitializing': _isInitializing,
      'services': {
        'adjust': AdjustService.instance.getServiceStatus(),
        'meta': MetaService.instance.getServiceStatus(),
        'revenuecat': RevenueCatService.instance.getServiceStatus(),
        'purchase': PurchaseService.instance.getServiceStatus(),
      },
      'initializationLog': _initializationLog,
    };
  }

  /// Reset initialization state (for testing)
  void reset() {
    _isInitialized = false;
    _isInitializing = false;
    _initializationLog.clear();

    if (kDebugMode) {
      print('🔄 App initialization service reset');
    }
  }
}
